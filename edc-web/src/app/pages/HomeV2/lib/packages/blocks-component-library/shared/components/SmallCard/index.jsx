import React, { useState } from 'react';
import { string, number, bool, any } from 'prop-types';
import classNames from 'classnames';
import { unescape } from 'lodash';

import ErrorImagePlaceholder from 'centralized-design-system/src/MUIComponents/icons/ErrorImagePlaceholder';

import './SmallCard.scss';
import Tooltip from 'centralized-design-system/src/Tooltip';

export const SmallCard = ({
  imgUrl,
  altText,
  title,
  completedPercentage,
  headline,
  navigationUrl,
  isCardDeleted = false,
  action,
  secondLine,
  children
}) => {
  const [imageError, setImageError] = useState(imgUrl === undefined);
  const imgError = () => {
    setImageError(true);
  };

  return (
    <div
      className={classNames('ed-ui SmallCardBlock ', {
        'no-progress-bar-card': !completedPercentage,
        'progress-bar-card': completedPercentage
      })}
    >
      <div className="SmallCardContent">
        <a className="SmallCardLink" href={navigationUrl}>
          {!imageError && (
            <div className="img-block m-margin-right">
              <img alt={altText} src={imgUrl} onError={imgError} />
            </div>
          )}
          {imageError && (
            <div className="err-img-block img-block m-margin-right">
              <ErrorImagePlaceholder />
            </div>
          )}

          <div className="SmallCardInfo">
            {headline && <span className="SmallCardHeadline">{headline}</span>}
            <Tooltip
              message={unescape(title)}
              isHtmlIncluded={true}
              pos="top"
              customClass="small-card-tooltip"
            >
              <p
                className={classNames('small-card__title text-ellipsis text-left', {
                  'gray-text': isCardDeleted
                })}
              >
                {unescape(title)}
              </p>
            </Tooltip>
            {secondLine && <p className="SmallCardSecondLine">{secondLine}</p>}
          </div>
        </a>

        {action && <div className="SmallCardAction">{action}</div>}
      </div>
      {children}
    </div>
  );
};

SmallCard.propTypes = {
  title: string,
  author: string,
  skillLevel: string,
  translatedSkillLevelDesc: string,
  duration: string,
  completedPercentage: number,
  imgUrl: string,
  altText: string,
  type: string,
  language: string,
  navigationUrl: string,
  isCardDeleted: bool,
  headline: string,
  action: any,
  secondLine: string,
  children: any
};

export const SmallCardFooter = ({ children }) => {
  return <div className="SmallCardFooter">{children}</div>;
};

SmallCardFooter.propTypes = {
  children: any
};

export const SmallCardWrapper = ({ children }) => {
  return <div className="SmallCardWrapper">{children}</div>;
};

SmallCardWrapper.propTypes = {
  children: any
};
